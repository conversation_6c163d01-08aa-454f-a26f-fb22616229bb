<?php

const DD_THEME_VERSION = '1.0.7';
const DD_TEXTDOMAIN = 'linguanova';

function dd_enqueue_admin_style(){
    wp_register_style('custom_wp_admin_css', get_template_directory_uri() . '/assets/css/admin-style.css', array(), DD_THEME_VERSION);
    wp_enqueue_style('custom_wp_admin_css');

    wp_register_style('custom_wp_admin_js', get_template_directory_uri() . '/assets/js/admin-js.js', array(), DD_THEME_VERSION);
    wp_enqueue_style('custom_wp_admin_js');
}
add_action('admin_enqueue_scripts', 'dd_enqueue_admin_style');

function dd_load_stylesheets() {
  wp_register_style('style', get_template_directory_uri() . '/assets/css/style.css', array(), DD_THEME_VERSION);
  wp_enqueue_style('style');

  wp_register_style( 'swiper', get_template_directory_uri() . '/assets/css/swiper.css', array(), DD_THEME_VERSION);
	wp_enqueue_style( 'swiper' );
}

add_action('wp_enqueue_scripts', 'dd_load_stylesheets');

function dd_load_js() {

  wp_deregister_script( 'jquery' );

	wp_register_script( 'jquery', get_template_directory_uri() . '/assets/js/jquery.js', array(), DD_THEME_VERSION);
	wp_enqueue_script( 'jquery' );

  wp_register_script( 'swiper', get_template_directory_uri() . '/assets/js/swiper.js', array(), DD_THEME_VERSION);
	wp_enqueue_script( 'swiper' );

  wp_register_script('script', get_template_directory_uri() . '/assets/js/script.js', array(), DD_THEME_VERSION);
  wp_enqueue_script('script');
}

add_action('wp_enqueue_scripts', 'dd_load_js');

function dd_multilang_load() {
    load_theme_textdomain(DD_TEXTDOMAIN, get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'dd_multilang_load');